import os
import sys
import time
import base64
from pathlib import Path

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# --------------------------
# FastAPI Setup
# --------------------------
app = FastAPI(title="Tele Taleem PowerBill API", version="3.1")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --------------------------
# Reference Numbers File
# --------------------------
possible_paths = [
    Path.home() / "Desktop" / "reference numbers" / "ref_numbers.txt",  # Desktop folder
    Path(__file__).parent / "ref_numbers.txt",                          # fallback
]

ref_file_path = None
for p in possible_paths:
    if p.exists():
        ref_file_path = p
        break

if ref_file_path is None:
    print("❌ ref_numbers.txt not found in any known location!")
    sys.exit(1)

print(f"✅ Using reference numbers file: {ref_file_path}")

# --------------------------
# PDF Download Directory
# --------------------------
BASE_DIR = Path(__file__).parent
download_dir = BASE_DIR / "IESCO_Bills"
download_dir.mkdir(parents=True, exist_ok=True)

# Serve PDFs as static files
app.mount("/static", StaticFiles(directory=download_dir), name="static")

# --------------------------
# Selenium Chrome Driver
# --------------------------
def get_chrome_driver(headless=True):
    options = Options()
    if headless:
        options.add_argument("--headless=new")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-infobars")
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    return driver

# --------------------------
# Save PDF
# --------------------------
def save_pdf(driver, save_path):
    pdf = driver.execute_cdp_cmd("Page.printToPDF", {"printBackground": True, "format": "A4"})
    with open(save_path, "wb") as f:
        f.write(base64.b64decode(pdf["data"]))
    print(f"✅ Saved: {save_path}")

# --------------------------
# Download single bill
# --------------------------
def download_single_bill(driver, ref_number: str):
    driver.get("https://iescobill.pk/")
    
    # Enter reference number
    ref_input = WebDriverWait(driver, 15).until(
        EC.presence_of_element_located((By.ID, "reference"))
    )
    ref_input.clear()
    ref_input.send_keys(ref_number)
    
    # Click Check Bill
    driver.find_element(By.ID, "checkBill").click()
    time.sleep(2)
    
    # Click Open Bill
    open_btn = WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, "form#billForm button[type='submit']"))
    )
    open_btn.click()
    
    # Wait for print button
    WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, "button[onclick='window.print()']"))
    )
    time.sleep(1)
    
    # Save PDF
    save_path = download_dir / f"{ref_number}.pdf"
    save_pdf(driver, str(save_path))

# --------------------------
# API Endpoints
# --------------------------
@app.get("/api/run-file")
async def run_file_bills():
    with open(ref_file_path, "r") as f:
        ref_numbers = [line.strip() for line in f if line.strip()]

    if not ref_numbers:
        return JSONResponse(status_code=400, content={"error": "No reference numbers found"})

    driver = get_chrome_driver(headless=True)
    results = []
    try:
        for idx, ref in enumerate(ref_numbers, start=1):
            try:
                download_single_bill(driver, ref)
                results.append({"serial": idx, "refNumber": ref, "status": "✅ Completed"})
            except Exception as e:
                results.append({"serial": idx, "refNumber": ref, "status": f"❌ Failed: {e}"})
        return {"results": results}
    finally:
        driver.quit()

@app.post("/api/run-single")
async def run_single_bill(request: Request):
    data = await request.json()
    ref_number = data.get("refNumber")
    if not ref_number:
        return JSONResponse(status_code=400, content={"detail": "Reference number is required"})

    driver = get_chrome_driver(headless=True)
    try:
        download_single_bill(driver, ref_number)
        return {"message": f"Bill {ref_number} downloaded", "file_path": f"/static/{ref_number}.pdf"}
    finally:
        driver.quit()

@app.get("/api/health")
async def health_check():
    return {"status": "OK", "message": "API is running 🚀"}

# --------------------------
# CLI Mode
# --------------------------
def run_all_bills_from_file():
    with open(ref_file_path, "r") as f:
        ref_numbers = [line.strip() for line in f if line.strip()]

    if not ref_numbers:
        print("❌ No reference numbers found")
        return

    driver = get_chrome_driver(headless=False)
    try:
        for idx, ref in enumerate(ref_numbers, start=1):
            try:
                print(f"⚡ [{idx}] Downloading bill: {ref}")
                download_single_bill(driver, ref)
            except Exception as e:
                print(f"❌ [{idx}] Failed to download {ref}: {e}")
        print("🎉 All downloads finished.")
    finally:
        driver.quit()

# --------------------------
# Main
# --------------------------
if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--server":
        uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
    else:
        run_all_bills_from_file()
