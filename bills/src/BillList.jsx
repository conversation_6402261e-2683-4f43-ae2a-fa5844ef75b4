import React, { useEffect, useState } from "react";

function BillList({ goBack }) {
  const [bills, setBills] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch("http://localhost:8000/api/run-file")
      .then((res) => res.json())
      .then((data) => {
        setBills(data.results || []);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, []);

  const handleDownloadAll = () => {
    bills.forEach((bill) => {
      if (bill.status.includes("✅")) {
        const url = `http://localhost:8000/static/${bill.refNumber}.pdf`;
        const a = document.createElement("a");
        a.href = url;
        a.download = `${bill.refNumber}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    });
  };

  return (
    <div className="card p-4">
      <button className="btn btn-secondary mb-3" onClick={goBack}>
        ← Back
      </button>

      <h3 className="text-center mb-3">All Bills</h3>

      {loading && <p>⏳ Downloading bills...</p>}
      {!loading && bills.length === 0 && <p>No bills found</p>}

      {bills.length > 0 && (
        <>
          <table className="table table-bordered table-striped">
            <thead className="table-dark">
              <tr>
                <th>Reference Number</th>
                <th>Status</th>
                <th>View PDF</th>
              </tr>
            </thead>
            <tbody>
              {bills.map((bill) => (
                <tr key={bill.refNumber}>
                  <td>{bill.refNumber}</td>
                  <td>{bill.status}</td>
                  <td>
                    {bill.status.includes("✅") && (
                      <a
                        href={`http://localhost:8000/static/${bill.refNumber}.pdf`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="btn btn-success btn-sm"
                      >
                        View
                      </a>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <button className="btn btn-primary mt-3" onClick={handleDownloadAll}>
            Download All PDFs
          </button>
        </>
      )}
    </div>
  );
}

export default BillList;
