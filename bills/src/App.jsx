import React, { useState } from "react";
import DownloadBill from "./DownloadBill";
import BillList from "./BillList";
import "bootstrap/dist/css/bootstrap.min.css";
import './App.css';


function App() {
  const [viewList, setViewList] = useState(false);

  return (
    <div className="container mt-5">
      <div className="app-header text-center mb-4">
        <img
          src="/teletaleem.png"
          alt="Logo"
          className="img-fluid"
          style={{ maxWidth: "80px" }}
        />
        <h1 className="mt-3 app-title">Tele Taleem PowerBill</h1>
      </div>

      <div className="card shadow-sm p-4">
        {!viewList ? (
          <DownloadBill showList={() => setViewList(true)} />
        ) : (
          <BillList goBack={() => setViewList(false)} />
        )}
      </div>
    </div>
  );
}

export default App;
