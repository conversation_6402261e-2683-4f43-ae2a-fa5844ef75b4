import React, { useState } from "react";

export default function DownloadBill({ showList }) {
  const [refNumber, setRefNumber] = useState("");
  const [status, setStatus] = useState("");
  const [statusList, setStatusList] = useState([]);
  const [loadingFile, setLoadingFile] = useState(false);

  const downloadBill = async () => {
    if (!refNumber) {
      setStatus("Enter a reference number!");
      return;
    }
    setStatus("Downloading...");
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-single", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refNumber }),
      });
      const data = await res.json();
      if (res.ok) setStatus(`✅ ${data.message}`);
      else setStatus(`❌ ${data.detail}`);
    } catch {
      setStatus("❌ Error running script");
    }
  };

  const downloadAllBills = async () => {
    setLoadingFile(true);
    setStatusList([]);
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-file");
      const data = await res.json();
      if (res.ok) setStatusList(data.results || []);
      else setStatusList([{ refNumber: "-", status: data.error }]);
    } catch {
      setStatusList([{ refNumber: "-", status: "❌ Error running script" }]);
    } finally {
      setLoadingFile(false);
    }
  };

  return (
    <div className="card p-4">
      <h3 className="text-center mb-3 text-success">Download Single Bill</h3>
      <div className="input-group mb-3">
        <input
          type="text"
          className="form-control"
          placeholder="Enter Reference Number"
          value={refNumber}
          onChange={(e) => setRefNumber(e.target.value)}
        />
        <button className="btn btn-primary" onClick={downloadBill}>
          Download
        </button>
      </div>
      <p className="fw-bold">{status}</p>

      <hr />

      <h3 className="text-center mb-3 text-success">Download Bills from File</h3>
      <div className="d-flex justify-content-center mb-3">
        <button
          className="btn btn-warning"
          onClick={downloadAllBills}
          disabled={loadingFile}
        >
          {loadingFile ? "Downloading..." : "Download All Bills"}
        </button>
        <button className="btn btn-secondary ms-2" onClick={showList}>
          View List
        </button>
      </div>

      {statusList.length > 0 && (
        <table className="table table-bordered table-striped mt-3">
          <thead className="table-dark">
            <tr>
              <th>Reference Number</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {statusList.map((item, idx) => (
              <tr key={idx}>
                <td>{item.refNumber}</td>
                <td>{item.status}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}
